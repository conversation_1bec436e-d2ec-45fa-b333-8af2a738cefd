@tailwind base;
@tailwind components;
@tailwind utilities;

/* Azo Sans Font Face Declarations */
@font-face {
  font-family: "AzoSans";
  src: url("/fonts/azo-sans/AzoSans-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "AzoSans";
  src: url("/fonts/azo-sans/AzoSans-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "AzoSans";
  src: url("/fonts/azo-sans/AzoSans-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "AzoSans";
  src: url("/fonts/azo-sans/AzoSans-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Inter Font Face Declarations */
@font-face {
  font-family: "Inter";
  src: url("/fonts/inter/Inter-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Inter";
  src: url("/fonts/inter/Inter-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Inter";
  src: url("/fonts/inter/Inter-Light-BETA.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Inter";
  src: url("/fonts/inter/Inter-Medium.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Inter";
  src: url("/fonts/inter/Inter-SemiBold.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

:root {
  --color-primary: var(--color-primary);
  --color-highlight: var(--color-highlight);
  --color-background: var(--color-background);
  --color-secondary: var(--color-secondary);
  --color-text: var(--color-text-primary);
  --color-text-secondary: var(--color-text-secondary);
  --color-success: var(--color-success);
  --color-error: var(--color-error);
  --color-border: var(--color-border);
  --color-on-background: var(--color-on-background);
  --color-surface: var(--color-surface);
  --color-on-surface: var(--color-on-surface);
  /* start of date picker */
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;

  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;

  --primary: 192 100% 15%;
  --primary-foreground: 210 40% 98%;

  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;

  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;

  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;

  --radius: 0.5rem;

  /* end of date picker */
}
body {
  font-family: var(
    --body-font-family,
    var(--font-azo-sans),
    "AzoSans",
    sans-serif
  );
  overflow: hidden;
}

.react-datepicker__input-container input:focus {
  border: 0px solid transparent;
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Focus styles for interactive elements */
  button:focus-visible,
  a:focus-visible,
  [role="button"]:focus-visible,
  [tabindex]:focus-visible {
    /* @apply outline-none ring-2 ring-primary ring-offset-2; */
  }

  /* Ensure custom buttons have proper focus styles */
  button:not(.focus-styled):focus-visible {
    @apply outline-2 outline-primary outline-offset-2;
    outline-style: solid;
  }

  /* Ensure all buttons are keyboard accessible */
  button {
    cursor: pointer;
  }

  button:disabled {
    cursor: not-allowed;
  }

  /* Ensure interactive elements are keyboard accessible */
  [role="button"]:not(button):not(a) {
    cursor: pointer;
  }

  [role="button"]:not(button):not(a):focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2;
  }

  /* React-Select focus-visible styles - only show focus ring during keyboard navigation */
  .react-select__control:focus-visible {
    box-shadow: 0 0 0 2px hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
  }

  /* Remove focus styles when not using keyboard navigation */
  .react-select__control:focus:not(:focus-visible) {
    box-shadow: none !important;
    border-color: var(--color-border) !important;
  }

  /* Override react-select's default focus styles completely */
  .react-select__control--is-focused:not(:focus-visible) {
    box-shadow: none !important;
    border-color: var(--color-border) !important;
  }

  /* Only show focus styles during keyboard navigation */
  .react-select__control--is-focused:focus-visible {
    box-shadow: 0 0 0 2px hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
  }

  /* React-Select accessibility improvements */
  .react-select__dropdown-indicator,
  .react-select__clear-indicator,
  .react-select__multi-value__remove {
    cursor: pointer;
  }

  /* Ensure keyboard navigation works for react-select button elements */
  .react-select__dropdown-indicator:focus-visible,
  .react-select__clear-indicator:focus-visible,
  .react-select__multi-value__remove:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
}

.RadioGroupRoot {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.RadioGroupItem {
  background-color: white;
  border-radius: 100%;
}

.RadioGroupIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.RadioGroupIndicator::after {
  content: "";
  display: block;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  background-color: var(--color-primary);
}

.markDown ol li {
  margin-top: 8px;
}

.markDown pre {
  overflow-x: scroll;
  margin-top: 8px;
  margin-bottom: 8px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  display: none;
}

.rdp-vhidden {
  @apply hidden;
}
