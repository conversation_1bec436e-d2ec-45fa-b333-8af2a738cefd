/**
 * Global accessibility fixes for react-select components
 * This script automatically adds missing role attributes to react-select elements
 * to fix accessibility issues without modifying the dropdown component directly.
 */

export function initializeAccessibilityFixes() {
  // Function to add missing role attributes to react-select elements
  function addReactSelectRoles() {
    // Fix dropdown indicators that should have role="button"
    const dropdownIndicators = document.querySelectorAll(
      ".react-select__dropdown-indicator"
    );
    dropdownIndicators.forEach((element) => {
      if (!element.getAttribute("role")) {
        element.setAttribute("role", "button");
        element.setAttribute("aria-label", "Open dropdown menu");
        element.setAttribute("tabindex", "0");
      }
    });

    // Fix clear indicators that should have role="button"
    const clearIndicators = document.querySelectorAll(
      ".react-select__clear-indicator"
    );
    clearIndicators.forEach((element) => {
      if (!element.getAttribute("role")) {
        element.setAttribute("role", "button");
        element.setAttribute("aria-label", "Clear selection");
        element.setAttribute("tabindex", "0");
      }
    });

    // Fix multi-value remove buttons that should have role="button"
    const multiValueRemoves = document.querySelectorAll(
      ".react-select__multi-value__remove"
    );
    multiValueRemoves.forEach((element) => {
      if (!element.getAttribute("role")) {
        element.setAttribute("role", "button");
        const valueText =
          element
            .closest(".react-select__multi-value")
            ?.querySelector(".react-select__multi-value__label")?.textContent ||
          "item";
        element.setAttribute("aria-label", `Remove ${valueText}`);
        element.setAttribute("tabindex", "0");
      }
    });

    // Fix single value displays that should have proper accessibility
    const singleValues = document.querySelectorAll(
      ".react-select__single-value"
    );
    singleValues.forEach((element) => {
      if (!element.getAttribute("role")) {
        element.setAttribute("role", "button");
        element.setAttribute("aria-readonly", "true");
      }
    });

    // Fix control elements that should have proper combobox role
    const controls = document.querySelectorAll(".react-select__control");
    controls.forEach((element) => {
      if (!element.getAttribute("role")) {
        element.setAttribute("role", "combobox");
        element.setAttribute("aria-haspopup", "listbox");

        // Check if menu is open
        const selectContainer = element.closest(".react-select__container");
        const menuIsOpen =
          selectContainer?.querySelector(".react-select__menu") !== null;
        element.setAttribute("aria-expanded", menuIsOpen.toString());
      }
    });

    // Fix menu options that should have proper option role
    const options = document.querySelectorAll(".react-select__option");
    options.forEach((element) => {
      if (!element.getAttribute("role")) {
        element.setAttribute("role", "option");
        const isSelected = element.classList.contains(
          "react-select__option--is-selected"
        );
        element.setAttribute("aria-selected", isSelected.toString());
      }
    });

    // Fix menu containers that should have proper listbox role
    const menus = document.querySelectorAll(".react-select__menu");
    menus.forEach((element) => {
      if (!element.getAttribute("role")) {
        element.setAttribute("role", "listbox");
      }
    });

    // Fix specific problematic CSS classes from accessibility report
    const problematicElements = document.querySelectorAll(
      [
        ".css-7pg0cj-a11yText",
        ".css-9np160-singleValue",
        ".css-1wy0on6",
        ".css-1hyfx7x",
        ".css-187t9pu-indicatorContainer",
      ].join(", ")
    );

    problematicElements.forEach((element) => {
      // Determine appropriate role based on element characteristics
      if (
        element.classList.contains("css-9np160-singleValue") ||
        element.classList.contains("css-7pg0cj-a11yText")
      ) {
        if (!element.getAttribute("role")) {
          element.setAttribute("role", "button");
          element.setAttribute("aria-readonly", "true");
          element.setAttribute("aria-label", "Open dropdown menu");
          element.setAttribute("tabindex", "0");
        }
      } else if (element.classList.contains("css-187t9pu-indicatorContainer")) {
        if (!element.getAttribute("role")) {
          element.setAttribute("role", "button");
          element.setAttribute("aria-label", "Dropdown indicator");
          element.setAttribute("tabindex", "0");
        }
      } else if (
        element.closest(".react-select__dropdown-indicator") ||
        element.closest(".react-select__clear-indicator") ||
        element.closest(".react-select__multi-value__remove")
      ) {
        if (!element.getAttribute("role")) {
          element.setAttribute("role", "button");
          element.setAttribute("tabindex", "0");
        }
      }
    });

    // Fix HTML5 landmark elements that need proper ARIA attributes
    const landmarkElements = document.querySelectorAll(
      'main, footer, section, div[aria-label*="content"], div[class*="flex"][class*="flex-col"], div[class*="max-md:w-full"], div[class*="bg-on-background"], div[class*="items-center"], div[class*="mb-4"][class*="mt-4"]'
    );

    landmarkElements.forEach((element) => {
      // Fix main elements
      if (element.tagName.toLowerCase() === "main") {
        if (!element.getAttribute("role")) {
          element.setAttribute("role", "main");
        }
        if (!element.getAttribute("aria-label")) {
          element.setAttribute("aria-label", "Main content");
        }
      }

      // Fix footer elements
      else if (element.tagName.toLowerCase() === "footer") {
        if (!element.getAttribute("role")) {
          element.setAttribute("role", "contentinfo");
        }
        if (!element.getAttribute("aria-label")) {
          element.setAttribute("aria-label", "Footer");
        }
      }

      // Fix section elements that should be main content
      else if (
        element.tagName.toLowerCase() === "section" &&
        element.classList.toString().includes("w-full") &&
        element.classList.toString().includes("flex-col")
      ) {
        if (!element.getAttribute("role")) {
          element.setAttribute("role", "main");
        }
        if (!element.getAttribute("aria-label")) {
          element.setAttribute("aria-label", "Main content area");
        }
      }

      // Fix div elements that should be main content areas
      else if (
        element.tagName.toLowerCase() === "div" &&
        (element.getAttribute("aria-label")?.includes("content") ||
          (element.classList.toString().includes("flex") &&
            element.classList.toString().includes("flex-col") &&
            element.classList.toString().includes("justify-center")))
      ) {
        if (!element.getAttribute("role")) {
          element.setAttribute("role", "main");
        }
        if (!element.getAttribute("aria-label")) {
          element.setAttribute("aria-label", "Application form content");
        }
      }
    });

    // Fix additional elements that act as buttons but use non-button tags
    const additionalElements = document.querySelectorAll(
      'span[aria-live="polite"], div[class*="react-select__indicator"], span[id*="react-select-"][class*="css-7pg0cj-a11yText"], span[class*="css-"][class*="a11yText"], div.react-select__indicators'
    );

    additionalElements.forEach((element) => {
      // Skip if already has role="button" or is inside a proper button
      if (
        element.getAttribute("role") === "button" ||
        element.closest("button")
      )
        return;

      // Check if element acts as a button (has click handlers, tabindex, or is interactive)
      const hasClickHandler =
        (element as any).onclick || element.getAttribute("onclick");
      const hasTabindex = element.getAttribute("tabindex");
      const isInteractive =
        element.classList.contains("react-select__dropdown-indicator") ||
        element.classList.contains("react-select__clear-indicator") ||
        element.classList.contains("react-select__multi-value__remove");

      // Special case for aria-live elements with a11yText class that need role="button"
      const isA11yTextElement =
        element.classList.toString().includes("a11yText") ||
        element.classList.toString().includes("A11yText");
      const isAriaLiveElement = element.getAttribute("aria-live") === "polite";

      // Special case for react-select indicators container
      const isIndicatorsContainer = element.classList.contains(
        "react-select__indicators"
      );

      if (
        hasClickHandler ||
        hasTabindex ||
        isInteractive ||
        isIndicatorsContainer ||
        (isAriaLiveElement && isA11yTextElement)
      ) {
        // Remove existing role if it's "log" and replace with "button"
        if (element.getAttribute("role") === "log") {
          element.removeAttribute("role");
        }

        element.setAttribute("role", "button");
        if (!element.getAttribute("tabindex")) {
          element.setAttribute("tabindex", "0");
        }
        if (!element.getAttribute("aria-label")) {
          if (element.classList.contains("react-select__dropdown-indicator")) {
            element.setAttribute("aria-label", "Open dropdown menu");
          } else if (
            element.classList.contains("react-select__clear-indicator")
          ) {
            element.setAttribute("aria-label", "Clear selection");
          } else if (
            element.classList.contains("react-select__multi-value__remove")
          ) {
            const valueText =
              element
                .closest(".react-select__multi-value")
                ?.querySelector(".react-select__multi-value__label")
                ?.textContent || "item";
            element.setAttribute("aria-label", `Remove ${valueText}`);
          } else if (isA11yTextElement) {
            element.setAttribute("aria-label", "Screen reader text");
          } else if (isIndicatorsContainer) {
            element.setAttribute("aria-label", "Dropdown controls");
          } else {
            element.setAttribute("aria-label", "Interactive element");
          }
        }
      }
    });
  }

  // Function to handle keyboard events for elements with role="button"
  function handleKeyboardEvents(event: KeyboardEvent) {
    const target = event.target as HTMLElement;

    // Handle Enter and Space key presses on elements with role="button"
    if (
      target.getAttribute("role") === "button" &&
      (event.key === "Enter" || event.key === " ")
    ) {
      event.preventDefault();

      // Trigger click event
      target.click();
    }
  }

  // Initial fix on page load
  addReactSelectRoles();

  // Set up mutation observer to fix newly added react-select elements
  const observer = new MutationObserver((mutations) => {
    let shouldReapplyFixes = false;

    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;

          // Check if the added node or its children contain react-select elements
          if (
            element.classList?.contains("react-select__container") ||
            element.querySelector?.(".react-select__container") ||
            element.classList?.toString().includes("css-")
          ) {
            shouldReapplyFixes = true;
          }
        }
      });
    });

    if (shouldReapplyFixes) {
      // Use setTimeout to ensure DOM is fully updated
      setTimeout(addReactSelectRoles, 0);
    }
  });

  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  // Add keyboard event listener
  document.addEventListener("keydown", handleKeyboardEvents);

  // Also fix elements when react-select menus open/close
  document.addEventListener("click", () => {
    setTimeout(addReactSelectRoles, 100);
  });

  // Return cleanup function
  return () => {
    observer.disconnect();
    document.removeEventListener("keydown", handleKeyboardEvents);
  };
}

// Auto-initialize when DOM is ready
if (typeof window !== "undefined") {
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeAccessibilityFixes);
  } else {
    initializeAccessibilityFixes();
  }
}
